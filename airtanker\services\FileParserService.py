from typing import Dict, List
from main import airtanker_app
from services.DatabaseService import DatabaseService
from services.GeminiService import GeminiService
from services.CustomerService import CustomerService
from models.NameMatch import Status, Origin
from google.genai import types
import base64
import json
import pandas as pd

class FileParserService:

    def __init__(self):
        pass

    def parse_customer_timesheets(self, *, files, template_ids, db_service: DatabaseService) -> pd.DataFrame:

        # db_service = DatabaseService()
        # db_service.connect() # We are already connected...
        
        all_dfs = []
        templates = db_service.get_templates()
        template_map = {int(template['id']): template for template in templates}

        # TODO: Check if we need to retrieve the customers before the call to gemini, so we can send it to gemini and have it match the customers for us.
        
        for idx, (file, template_id) in enumerate(zip(files, template_ids)):
            print(f"\n=== Processing File {idx + 1} ===")
            print(f"Filename: {file.filename}")
            print(f"Template ID: {template_id}")

            # TODO: Uncomment this when stop testing
            # Check if file has already been processed
            # file_id, file_already_processed = db_service.insert_filename_get_id(filename=file.filename, sheet_name='', source_type='Customer Timesheets')
            # if file_already_processed:
            #     print(f"File {file.filename} has already been processed. Skipping...")
            #     continue

            # Get template data
            template = template_map.get(int(template_id))
            if not template:
                print(f"ERROR: Template with ID {template_id} not found!")
                continue

            # Convert file to base64
            file.seek(0)  # Reset file pointer
            file_content = file.read()
            file_base64 = base64.b64encode(file_content).decode('utf-8')

            # Right before sending data to gemini, retrieve customers from active work orders and send them to gemini.

            # Prepare the AI prompt with template data
            ai_prompt = template['prompt']
            response_schema = template['response_schema']

            # Create the complete prompt with context
            complete_prompt = f"File Name: {file.filename} \nMime Type: {file.content_type}\n\n{ai_prompt}"

            # Generate 'parts'
            parts = [
                types.Part.from_bytes(
                    mime_type=file.content_type,
                    data=file_base64,
                ),
                types.Part.from_text(text=complete_prompt),
            ]

            # Make the AI call
            print(f"\n--- Making AI Call ---")
            try:
                gemini_service = GeminiService()

                # Generate AI response
                print("🤖 Sending prompt to Gemini...")
                print("⏳ This may take a few moments...")

                ai_response = gemini_service.generate_content(parts, response_schema)

                print(f"✅ AI Response received ({len(ai_response)} characters)")

                # Eventhough we are asking gemini to return json when sending back the reponse, it sends it in str chunks, so we need to reparse it.
                try:
                    parsed_response = json.loads(ai_response)
                    print(f"\n--- Parsed JSON Response to {type(parsed_response)} ---")
                    print(json.dumps(parsed_response, indent=2))

                    # Validate against expected schema
                    if 'timeentries' in parsed_response:
                        print("✅ Response appears to contain timesheet data")
                    else:
                        raise Exception("Response did not match expected timesheet format")
                    
                except Exception as e:
                    print(f"⚠️  Warning: Error parsing AI response: {e}")
                    raise Exception(f"Error parsing AI response: {e}")

            except Exception as ai_error:
                print(f"❌ Error calling Gemini AI: {ai_error}")
                airtanker_app.logger.exception("Gemini AI call failed")

            timeentries = parsed_response['timeentries']
            # Create a df from the timeentries
            df = pd.DataFrame(timeentries)
            # Parse the date
            df["Date"] = pd.to_datetime(df["Date"], format="%m-%d-%Y")
            # Add a new column with the file_id for all rows of this df
            # df["FileID"] = file_id # TODO: Uncomment this when stop testing

            all_dfs.append(df) # Add the df to the list of all dfs
            print("=" * 50)

        # Now we have parsed all the files
        if not all_dfs:
            print("No new data to process. Returning empty DataFrame.")
            return pd.DataFrame()
        
        # db_service.disconnect() # We should disconnect in the calling function.
        
        combined_df = pd.concat(all_dfs, ignore_index=True) # glue all dfs together
        return combined_df
    
    def parse_customer_names(self, *, df: pd.DataFrame, all_active_work_orders, db_service: DatabaseService) -> {pd.DataFrame}:
        # db_service = DatabaseService()
        # db_service.connect() # we are already connected...

        # Filter for lines that do not have a CustomerID assigned
        needs_id_mask = df[df['CustomerID'].isna() | (df['CustomerID'] == '')]

        lines_without_customer_id = df[needs_id_mask]

        # Nothing to do?
        if lines_without_customer_id.empty:
            print("No lines without customer ID. Returning original DataFrame as it is ready for next step.")
            return df

        customer_names = lines_without_customer_id["CustomerName"].unique().tolist()

        # extract the distinct customers (name and id) from all_active_work_order_entries
        seen = set()
        active_customers = []
        for wo in all_active_work_orders:
            key = (wo['CustomerName'], wo['CustomerID'])
            if key not in seen:
                seen.add(key)
                active_customers.append(
                    {
                        'customer_id': wo['CustomerID'],
                        'customer_name': wo['CustomerName'],
                    }
                )

        # use the customer service to match the names
        customer_matches = CustomerService(db_service).match_customers(customer_names, active_customers)
        # customer_matches should return somthing like this:
        # [
        #   NameMatch(name='ACME Corporation', status=Status.MATCHED, origin=Origin.ACTIVE_WO, ids=[101]),
        #   NameMatch(name='Beta Industries, Inc.', status=Status.AMBIGUOUS, origin=Origin.ACTIVE_WO, ids=[102, 103]),
        #   NameMatch(name='Delta Co', status=Status.MATCHED, origin=Origin.DATABASE, ids=[104]),
        #   NameMatch(name='Epsilon Inc', status=Status.AMBIGUOUS, origin=Origin.DATABASE, ids=[105, 106]),
        #   NameMatch(name='Gamma & Sons LLC', status=Status.NOT_FOUND, origin=None, ids=None),
        # ]

        print(f"Customer Matches:\n{customer_matches}")

        df_result = df.copy()

        # Initialize new columns if they don't exist
        if 'CustomerID' not in df_result.columns:
            df_result['CustomerID'] = None
        if 'PossibleCustomerIDs' not in df_result.columns:
            df_result['PossibleCustomerIDs'] = None
        if 'ErrorMessage' not in df_result.columns:
            df_result['ErrorMessage'] = None

        # Create a mapping from customer names to match results for quick lookup
        customer_match_map = {match.name: match for match in customer_matches}

        # Get customer names from database for name replacement
        all_db_customers = db_service.get_customers()
        customer_id_to_name = {customer['CustomerID']: customer['CustomerName'] for customer in all_db_customers}

        # Handle results for each row that needs customer ID assignment
        for idx, row in df_result.iterrows():
            # Skip rows that already have CustomerID assigned
            if pd.notna(row['CustomerID']) and row['CustomerID'] != '':
                continue

            customer_name = row['CustomerName']
            match_result = customer_match_map.get(customer_name)

            if not match_result:
                # This shouldn't happen, but handle gracefully (Just in case bad template configured)
                df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} was not processed during matching."
                airtanker_app.logger.error(f"Customer {customer_name} was not processed during matching. Error in FileParserService.parse_customer_names()")
                continue

            # Handle different match scenarios
            if match_result.status == Status.MATCHED:
                if match_result.origin == Origin.ACTIVE_WO:
                    # Perfect match from active work orders - assign CustomerID directly
                    df_result.at[idx, 'CustomerID'] = match_result.ids[0]
                    # Update CustomerName with the correct DB name
                    if match_result.ids[0] in customer_id_to_name:
                        df_result.at[idx, 'CustomerName'] = customer_id_to_name[match_result.ids[0]]

                elif match_result.origin == Origin.DATABASE:
                    # Match from database - add to PossibleCustomerIDs with warning
                    df_result.at[idx, 'PossibleCustomerIDs'] = str(match_result.ids[0])
                    df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} not found on active work orders. Selected {customer_name} from database. Please make sure the customer has an active work order and reupload file. Or skip this record."
                    # Update CustomerName with the correct DB name
                    if match_result.ids[0] in customer_id_to_name:
                        df_result.at[idx, 'CustomerName'] = customer_id_to_name[match_result.ids[0]]

            elif match_result.status == Status.AMBIGUOUS:
                # Multiple matches - add all IDs to PossibleCustomerIDs
                ids_str = ','.join(map(str, match_result.ids))
                df_result.at[idx, 'PossibleCustomerIDs'] = ids_str

                if match_result.origin == Origin.ACTIVE_WO:
                    df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} has multiple matches. Please select the correct one."
                elif match_result.origin == Origin.DATABASE:
                    df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} has multiple matches. Select the correct one and make sure the customer has an active work order. Or skip this record."

            elif match_result.status == Status.NOT_FOUND:
                # No match found
                df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} not found on internal records. Select one from this list, or verify work order and customer name and reupload file. Maybe a custom template for this file is needed."

        # Print summary of results
        matched_count = len(df_result[(df_result['CustomerID'].notna()) & (df_result['CustomerID'] != '')])
        error_count = len(df_result[df_result['ErrorMessage'].notna()])
        print(f"📈 Summary: {matched_count} rows with direct CustomerID assignment, {error_count} rows with errors/warnings")

        # db_service.disconnect() # we should disconnect in the calling function.

        return df_result


    def parse_employee_names(self, *, df: pd.DataFrame, all_active_work_orders, db_service: DatabaseService) -> pd.DataFrame:

        # df should not have EmployeeID assigned

        # Extract the distinct employee names from the df
        employee_names = df["EmployeeName"].unique().tolist()

        # extract the distinct employees (name and id) from all_active_work_order_entries
        seen = set()
        active_employees = []
        for wo in all_active_work_orders:
            key = (wo['EmployeeName'], wo['EmployeeID'])
            if key not in seen:
                seen.add(key)
                active_employees.append(
                    {
                        'employee_id': wo['EmployeeID'],
                        'employee_name': wo['EmployeeName'],
                    }
                )

        # use service to match the names


        # handle the possible errors

        # return the df with the employee id added

        pass
